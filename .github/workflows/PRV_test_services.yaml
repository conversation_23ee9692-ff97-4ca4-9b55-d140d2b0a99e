name: Pull Request Validation
on: [pull_request, workflow_dispatch]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  BUILD_VERSION: ${{ github.sha }}
  DOCKER_NETWORK: stack

jobs:
  build:
    name: Build and launch testing service stack
    runs-on: [self-hosted, prv, worker]
    env:
      DOCKER_BUILDKIT: 1
      COMPOSE_DOCKER_CLI_BUILD: 1
    steps:
      - name: Create subdirectory
        run: |
          echo $(pwd)
          rm -rf ${{ github.sha }}
          mkdir ${{ github.sha }}
      - name: Echo and set ref and build version
        run: |
          echo ${{ github.sha }}
          echo ${{ github.ref }}
          echo ${{ github.event_name }}

      - name: Checkout latest code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}
          path: ${{ github.sha }}

      - name: Copy testing environment setup
        run: |
          echo $(pwd)
          echo ${{ github.sha }}

          cp -f settings/.env.local.sample settings/.env.local
          # Ensure empty newline is appended to the end of file
          echo >> settings/.env.local
          cp -f infrastructure/testing/Makefile .
          cp -f infrastructure/testing/docker-compose.yaml .

          # Add missing vars
          echo AMBEE_APIKEY=${{ secrets.AMBEE_APIKEY }} >> settings/.env.local
          echo WAPI_KEY=${{ secrets.WAPI_KEY }} >> settings/.env.local
          echo YOUTUBE_API_KEY=${{ secrets.YOUTUBE_API_KEY }} >> settings/.env.local
          echo COST_NOTIFICATION_BOT_TOKEN=${{ secrets.COST_NOTIFICATION_BOT_TOKEN }} >> settings/.env.local
          echo PIPELINE_RUN=true >> settings/.env.local
          echo OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }} >> settings/.env.local
          echo GROQ_API_KEY=${{ secrets.GROQ_API_KEY }} >> settings/.env.local
          echo VISUALCROSSING_APIKEY=${{ secrets.VISUALCROSSING_APIKEY }} >> settings/.env.local

          cat settings/.env.local
        working-directory: ${{ github.sha }}

      - name: Upload dotenv artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ github.sha }}.env
          path: ${{ github.sha }}/settings/.env.local
          if-no-files-found: error
          include-hidden-files: true

      - name: Build stack in docker
        run: |
          echo $(pwd)
          echo $(ls)
          make rebuild
        working-directory: ${{ github.sha }}

      - name: Bring up stack in docker
        run: make launch
        working-directory: ${{ github.sha }}

      - name: Provision infrastructure
        run: make provision_infrastructure
        working-directory: ${{ github.sha }}

      - name: Wait for the cluster
        run: make await_infrastructure
        working-directory: ${{ github.sha }}

      - name: Reinitialize opensearch indices, load samples
        run: |
          make reinitialize_indices
          make load_samples
        working-directory: ${{ github.sha }}

      - name: Directory prune
        run: rm -rf ${{ github.sha }}

  test_data_service:
    name: Test data_service
    runs-on: [self-hosted, prv, worker]
    needs: build
    steps:
      - name: Validate data_service availability
        run: docker run --rm --network $BUILD_VERSION-$DOCKER_NETWORK appropriate/curl --ipv4 --retry 5 --retry-connrefused http://data_service:8003/

      - name: Export latest OpenAPI schema from running service
        run: |
          docker run --rm --network $BUILD_VERSION-$DOCKER_NETWORK \
            -v ${{ github.workspace }}:/output \
            curlimages/curl \
            -s http://data_service:8003/openapi_stable.json -o /output/openapi_latest.json

      - name: Compare exported OpenAPI spec to committed version
        run: |
          set -e
          if ! diff -u services/data_service/openapi_stable.json openapi_latest.json; then
            echo "❌ The exported OpenAPI schema is different from the committed version."
            echo "Run 'make export_openapi' then update and commit services/data_service/openapi-stable.json if it is correct."
            exit 1
          fi

      - name: Run data service tests
        run: docker exec $BUILD_VERSION-data_service make test_data_service

  test_file_service:
    name: Test file_service
    runs-on: [self-hosted, prv, worker]
    needs: build
    steps:
      - name: Validate file_service availability
        run: docker run --rm --network $BUILD_VERSION-$DOCKER_NETWORK appropriate/curl --ipv4 --retry 5 --retry-connrefused http://file_service:8001/
      - name: Run file service tests
        run: docker exec $BUILD_VERSION-file_service make test_file_service

  test_mobile_service:
    name: Test mobile_service
    runs-on: [self-hosted, prv, worker]
    needs: build
    steps:
      - name: Validate mobile_service availability
        run: docker run --rm --network $BUILD_VERSION-$DOCKER_NETWORK appropriate/curl --ipv4 --retry 5 --retry-connrefused http://mobile_service:8005/
      - name: Run mobile service tests
        run: docker exec $BUILD_VERSION-mobile_service make test_mobile_service

  test_user_service:
    name: Test user_service
    runs-on: [self-hosted, prv, worker]
    needs: build
    steps:
      - name: Validate user_service availability
        run: docker run --rm --network $BUILD_VERSION-$DOCKER_NETWORK appropriate/curl --ipv4 --retry 5 --retry-connrefused http://user_service:8004/
      - name: Run user service tests
        run: docker exec $BUILD_VERSION-user_service make test_user_service

  test_serverless:
    name: Test serverless
    runs-on: [self-hosted, prv, worker]
    needs: build
    steps:
      - name: Download dotenv artifact
        uses: actions/download-artifact@v4
        with:
          name: ${{ github.sha }}.env
      - name: Run serverless tests
        run: docker run --env-file .env.local --rm --network $BUILD_VERSION-$DOCKER_NETWORK $BUILD_VERSION-serverless_test_handler make test_serverless

  test_integration:
    name: Test integration
    runs-on: [self-hosted, prv, worker]
    needs: build
    steps:
      - name: Download dotenv artifact
        uses: actions/download-artifact@v4
        with:
          name: ${{ github.sha }}.env
      - name: Run integration tests
        run: |
          docker run \
            --env-file .env.local \
            --rm \
            --network $BUILD_VERSION-$DOCKER_NETWORK \
            $BUILD_VERSION-serverless_test_handler \
            make test_serverless_integration

          # Run data service integration tests in a running container
          docker exec \
            $BUILD_VERSION-data_service \
            make test_data_service_integration

  test_wxcache:
    name: Test Wxcache
    needs: build
    runs-on: [self-hosted, prv, worker]
    steps:
      - name: Download dotenv artifact
        uses: actions/download-artifact@v4
        with:
          name: ${{ github.sha }}.env
      - name: Validate wxcache availability
        run: |
          docker run --rm --network $BUILD_VERSION-$DOCKER_NETWORK appropriate/curl --ipv4 --retry 5 --retry-connrefused http://wxcache:8006/
      - name: Test wxcache
        run: |
          docker run --env-file .env.local -e IS_CONTAINERIZED=true --rm --network $BUILD_VERSION-$DOCKER_NETWORK $BUILD_VERSION-wxcache_build_cache make test_unit

  test_wxcache_integration:
    name: Test Wxcache Integration
    needs: build
    runs-on: [self-hosted, prv, worker]
    steps:
      - name: Download dotenv artifact
        uses: actions/download-artifact@v4
        with:
          name: ${{ github.sha }}.env
      - name: Validate wxcache availability
        run: |
          docker run --rm --network $BUILD_VERSION-$DOCKER_NETWORK appropriate/curl --ipv4 --retry 5 --retry-connrefused http://wxcache:8006/
      - name: Test wxcache
        run: |
          docker run --env-file .env.local -e IS_CONTAINERIZED=true --rm --network $BUILD_VERSION-$DOCKER_NETWORK $BUILD_VERSION-wxcache_build_cache make test_integration

  cleanup:
    name: Cleanup Docker-compose stack
    runs-on: [self-hosted, prv, worker]
    needs:
      [
        test_data_service,
        test_file_service,
        test_mobile_service,
        test_user_service,
        test_serverless,
        test_integration,
        test_wxcache,
        test_wxcache_integration,
      ]
    if: always()
    steps:
      - name: Tear down docker
        env:
          DOCKER_BUILDKIT: 1
          COMPOSE_DOCKER_CLI_BUILD: 1
        run: |
          docker compose -p ${{ env.BUILD_VERSION }} down --remove-orphans
          docker network prune -f
          docker container prune -f
          docker volume prune -f
