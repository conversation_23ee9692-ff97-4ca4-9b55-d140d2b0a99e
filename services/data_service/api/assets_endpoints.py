from typing import Annotated, Sequence
from uuid import UUID

from fastapi import APIRouter, Depends, Query
from fastapi_injector import Injected
from pydantic import StringConstraints
from starlette.responses import StreamingResponse

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.domain.annotated_types import ASSET_ID_REGEX_PATTERN
from services.data_service.api.constants import AssetEndpointRoutes, DataServicePrefixes
from services.data_service.api.models.response.asset.fetch_asset_url_api_output import FetchAssetUrlAPIOutput
from services.data_service.application.use_cases.by_id.fetch_asset_by_id_use_case import FetchAssetByIdUseCase
from services.data_service.application.use_cases.by_id.fetch_asset_url_use_case import FetchAssetUrlUseCase

assets_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.ASSET}",
    tags=["assets"],
    responses={404: {"description": "Not found"}},
)


@assets_router.get(AssetEndpointRoutes.BY_ID)
async def fetch_asset_by_id(
    user_uuid: UUID = Depends(get_current_uuid),
    asset_id: str = Query(min_length=1, pattern=ASSET_ID_REGEX_PATTERN),
    fetch_assets_use_case: FetchAssetByIdUseCase = Injected(FetchAssetByIdUseCase),
) -> StreamingResponse:
    """
    Fetch and stream an asset by its ID.

    Retrieves an asset (such as an image, document, or other file) from the user's
    storage container and streams it back to the client. The asset is returned as
    a streaming response to efficiently handle large files.

    Args:
        user_uuid: The UUID of the authenticated user making the request
        asset_id: The unique identifier of the asset to fetch. Must match the
                 asset ID regex pattern and have minimum length of 1
        fetch_assets_use_case: Injected use case for fetching assets by ID

    Returns:
        StreamingResponse: A streaming response containing the asset content with
                          media type set to "image/jpeg" (TODO: dynamic media type)

    Raises:
        404: Asset not found or user doesn't have access to the asset
        400: Invalid asset ID format
        401: User not authenticated
    """
    asset_stream = await fetch_assets_use_case.execute_async(user_uuid=user_uuid, asset_id=asset_id)
    # TODO: media type
    return StreamingResponse(content=asset_stream, media_type="image/jpeg")


@assets_router.get(AssetEndpointRoutes.URL)
async def fetch_asset_url(
    user_uuid: UUID = Depends(get_current_uuid),
    asset_ids: Sequence[
        Annotated[
            str,
            StringConstraints(strip_whitespace=True, min_length=1, pattern=ASSET_ID_REGEX_PATTERN),
        ]
    ] = Query(...),
    fetch_assets_use_case: FetchAssetUrlUseCase = Injected(FetchAssetUrlUseCase),
) -> FetchAssetUrlAPIOutput:
    output = await fetch_assets_use_case.execute_async(user_uuid=user_uuid, asset_ids=asset_ids)
    return FetchAssetUrlAPIOutput.map(model=output)
