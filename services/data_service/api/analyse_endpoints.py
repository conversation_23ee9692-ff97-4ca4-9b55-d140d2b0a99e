from uuid import UUI<PERSON>

from fastapi import APIRouter, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.application.exceptions import BadRequestException, IncorrectOperationException, NoContentException
from services.data_service.api.constants import AnalyseEndpointRoutes, DataServicePrefixes
from services.data_service.api.models.output.event_correlation_api_output import EventCorrelationAPIOutput
from services.data_service.api.models.output.suggest_correlation_parameters_api_output import (
    SuggestCorrelationParametersAPIOutput,
)
from services.data_service.api.models.output.trend_detection_api_output import TrendDetectionAPIOutput
from services.data_service.api.models.request.analyze.event_correlation_api_request_input import (
    EventCorrelationAPIRequestInput,
)
from services.data_service.api.models.request.analyze.suggest_correlation_parameters_api_request_input import (
    SuggestCorrelationParametersAPIRequestInput,
)
from services.data_service.api.models.request.analyze.trend_detection_api_request_input import (
    TrendDetectionAPIRequestInput,
)
from services.data_service.application.use_cases.event_correlation.event_correlation_use_case import (
    EventCorrelationUseCase,
    EventCorrelationUseCaseInputBoundary,
)
from services.data_service.application.use_cases.event_correlation.suggest_correlation_parameters_use_case import (
    SuggestCorrelationParametersUseCase,
    SuggestCorrelationParametersUseCaseInputBoundary,
)
from services.data_service.application.use_cases.trend_detection_use_case import (
    TrendDetectionUseCase,
    TrendDetectionUseCaseInputBoundary,
)

analyse_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.ANALYSE}",
    tags=["analyse"],
    responses={404: {"description": "Not found"}},
)


@analyse_router.post(
    AnalyseEndpointRoutes.TREND_DETECT,
)
async def trend_detection_endpoint(
    input_boundary: TrendDetectionUseCaseInputBoundary = Depends(TrendDetectionAPIRequestInput.to_input_boundary),
    use_case: TrendDetectionUseCase = Injected(TrendDetectionUseCase),
) -> TrendDetectionAPIOutput:
    """
    Detect trends in time series data using linear regression analysis.

    This endpoint analyzes a sequence of numerical values to determine if there's a statistically
    significant upward or downward trend. It uses linear regression with configurable thresholds
    for R-squared (model fit) and relative slope (trend significance).

    **Algorithm:**
    - Applies linear regression to the data series
    - Checks if R-squared meets the minimum threshold for model fit
    - Calculates relative slope (coefficient / mean) to determine trend significance
    - Returns trend classification and statistical metrics

    **Use Cases:**
    - Analyzing health metrics over time (weight, blood pressure, etc.)
    - Detecting patterns in symptom severity
    - Identifying trends in environmental data

    **Request Body:**
    - `data_series`: Array of at least 2 numerical values representing the time series
    - `r2_threshold`: Minimum R-squared value for significant trend (default: 0.3)
    - `relative_slope_threshold`: Minimum absolute relative slope for trend detection (default: 0.01)

    **Response:**
    - `trend_result`: Classification as "UPWARD_TREND", "DOWNWARD_TREND", or "NO_TREND"
    - `coefficient`: Linear regression coefficient (slope)
    - `intercept`: Linear regression intercept
    - `relative_slope`: Coefficient divided by mean value (measure of trend strength)

    **Example:**
    ```json
    {
        "data_series": [1.0, 1.2, 1.5, 1.8, 2.1],
        "r2_threshold": 0.3,
        "relative_slope_threshold": 0.01
    }
    ```

    **Returns:**
    ```json
    {
        "trend_result": "UPWARD_TREND",
        "coefficient": 0.25,
        "intercept": 0.95,
        "relative_slope": 0.167
    }
    ```
    """
    result = await use_case.execute_async(input_boundary=input_boundary)
    return TrendDetectionAPIOutput.map(model=result)


@analyse_router.post(
    AnalyseEndpointRoutes.CORRELATE_EVENT,
)
async def event_correlation_endpoint(
    input_boundary: EventCorrelationUseCaseInputBoundary = Depends(EventCorrelationAPIRequestInput.to_input_boundary),
    use_case: EventCorrelationUseCase = Injected(EventCorrelationUseCase),
) -> EventCorrelationAPIOutput:
    try:
        result = await use_case.execute_async(input_boundary=input_boundary)
        if not result.data:
            raise NoContentException("No data available")
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
    except NoContentException as err:
        raise NoContentException(message=err.message) from err
    return EventCorrelationAPIOutput.map(model=result)


@analyse_router.post(
    AnalyseEndpointRoutes.CORRELATE_EVENT_SUGGEST_PARAMETERS,
)
async def suggest_correlation_parameters_endpoint(
    _: UUID = Depends(get_current_uuid),
    input_boundary: SuggestCorrelationParametersUseCaseInputBoundary = Depends(
        SuggestCorrelationParametersAPIRequestInput.to_input_boundary
    ),
    use_case: SuggestCorrelationParametersUseCase = Injected(SuggestCorrelationParametersUseCase),
) -> SuggestCorrelationParametersAPIOutput:
    result = await use_case.execute_async(input_boundary=input_boundary)
    if not result:
        raise NoContentException("No suggestion available")
    return SuggestCorrelationParametersAPIOutput.map(model=result)
