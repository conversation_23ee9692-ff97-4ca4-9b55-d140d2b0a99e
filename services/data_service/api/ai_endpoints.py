from uuid import UUID

from fastapi import API<PERSON><PERSON><PERSON>, Body, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.data_service.api.constants import AIEndpointRoutes, DataServicePrefixes
from services.data_service.api.models.request.ai.suggest_event_request_input import SuggestEventRequestInput
from services.data_service.application.use_cases.ai.suggest_event_use_case import (
    SuggestEventUseCase,
    SuggestEventUseCaseInputBoundary,
)
from services.data_service.application.use_cases.events.insert_event_inputs import InsertEventInputs

ai_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.AI}",
    tags=["ai"],
    responses={404: {"description": "Not found"}},
)


@ai_router.post(
    AIEndpointRoutes.SUGGEST_EVENT,
    response_model_exclude={"content_hash"},
    summary="Suggest Event from Natural Language",
    description="""
    Generate structured event data from natural language input using AI.

    This endpoint processes a natural language query and returns a structured event that can be
    inserted into the user's timeline. The AI analyzes the query to:

    - **Classify event type**: Determines the appropriate event category (exercise, nutrition, symptom, etc.)
    - **Extract temporal information**: Identifies timestamps, durations, and end times
    - **Apply user context**: Uses the user's timezone and existing templates for better suggestions
    - **Structure data**: Returns properly formatted event data ready for insertion

    **Examples of supported queries:**
    - "I had a big mac for lunch yesterday"
    - "Play football at 10am for 1 hour"
    - "Feeling tired and headache this morning"
    - "30 minute run in the park"

    The response includes all necessary fields to create an event, with timestamps adjusted
    to the user's timezone and content structured according to the detected event type.
    """,
    response_description="Structured event data ready for insertion, with type-specific fields populated based on the natural language input",
)
async def suggest_event_endpoint(
    input: SuggestEventRequestInput = Body(..., description="Natural language query describing the event to be suggested"),
    user_uuid: UUID = Depends(get_current_uuid),
    use_case: SuggestEventUseCase = Injected(SuggestEventUseCase),
) -> InsertEventInputs:
    return await use_case.execute(
        user_uuid=user_uuid, input_boundary=SuggestEventUseCaseInputBoundary(query=input.query)
    )
