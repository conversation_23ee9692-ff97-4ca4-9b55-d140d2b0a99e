from uuid import UUID

from fastapi import API<PERSON><PERSON><PERSON>, Body, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.responses.common_document_responses import CommonDocumentsIdsResponse, CommonDocumentsResponse
from services.base.api.validators import validate_input_size
from services.base.application.exceptions import (
    BadRequestException,
    DuplicateDocumentsFound,
    IncorrectOperationException,
)
from services.data_service.api.constants import DataServicePrefixes, RecordEndpointRoutes
from services.data_service.api.mappers.record_api_output_mapper import Record<PERSON>IOutputMapper
from services.data_service.api.models.output.records.record_api_output import RecordAPIOutput
from services.data_service.api.models.request.records.delete_record_api_request_input import (
    DeleteRecordAPIRequestInput,
)
from services.data_service.api.models.request.records.insert_record_api_request_input import (
    InsertRecordAPIRequestInput,
)
from services.data_service.application.use_cases.records.delete_record_by_id_use_case import DeleteRecordByIdUseCase
from services.data_service.application.use_cases.records.insert_record_use_case import InsertRecordUseCase
from services.data_service.application.use_cases.records.models.delete_record_input_boundary import (
    DeleteRecordInputBoundary,
)
from services.data_service.application.use_cases.records.models.insert_record_input_boundary import (
    InsertRecordInputBoundary,
)

record_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.RECORD}",
    tags=["record"],
    responses={404: {"description": "Not found"}},
    dependencies=[Depends(validate_input_size)],
)


@record_router.post(RecordEndpointRoutes.BASE)
async def insert_record_endpoint(
    request_input: InsertRecordAPIRequestInput = Body(...),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: InsertRecordUseCase = Injected(InsertRecordUseCase),
) -> CommonDocumentsResponse[RecordAPIOutput]:
    try:
        records = await use_case.execute_async(
            boundary=InsertRecordInputBoundary.map(model=request_input), owner_id=owner_id
        )
        documents_api_output = [RecordAPIOutputMapper.map(record) for record in records]
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="record duplicates found in the input payload") from err

    return CommonDocumentsResponse[RecordAPIOutput](documents=documents_api_output)


# TODO: delete if not needed
# @record_router.patch(RecordEndpointRoutes.BASE)
# async def update_record_endpoint(
#     request_input: UpdateRecordAPIRequestInput = Body(...),
#     owner_id: UUID = Depends(get_current_uuid),
#     use_case: UpdateRecordUseCase = Injected(UpdateRecordUseCase),
# ) -> CommonDocumentsResponse[RecordAPIOutput]:
#     try:
#         records = await use_case.execute_async(
#             boundary=UpdateRecordInputBoundary.map(model=request_input), owner_id=owner_id
#         )
#         documents_api_output = [RecordAPIOutputMapper.map(record) for record in records]
#     except IncorrectOperationException as err:
#         raise BadRequestException(message=err.message) from err
#     except DuplicateDocumentsFound as err:
#         raise BadRequestException(message="record duplicates found in the input payload") from err
#
#     return CommonDocumentsResponse[RecordAPIOutput](documents=documents_api_output)


@record_router.delete(RecordEndpointRoutes.BASE)
async def delete_record_endpoint(
    request_input: DeleteRecordAPIRequestInput = Body(...),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: DeleteRecordByIdUseCase = Injected(DeleteRecordByIdUseCase),
) -> CommonDocumentsIdsResponse:
    try:
        deleted_uuids = await use_case.execute_async(
            boundary=DeleteRecordInputBoundary.map(model=request_input), owner_id=owner_id
        )
        return CommonDocumentsIdsResponse(document_ids=deleted_uuids)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
